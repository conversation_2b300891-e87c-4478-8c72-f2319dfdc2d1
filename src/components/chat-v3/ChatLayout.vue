<template>
  <div class="chat-v3-container">
    <!-- 初始化加载遮罩 -->
    <div v-if="!isInitialized" class="global-loading">
      <div class="loading-spinner"></div>
      <div class="loading-text">正在准备聊天环境...</div>
    </div>

    <!-- 主要内容 -->
    <div v-if="isInitialized" class="main-wrapper">
      <ChatHeader
        v-if="showHeader"
        title="知识库AI助手"
        :is-fullscreen="$parent && $parent.isFullscreen"
        :is-minimized="$parent && $parent.isMinimized"
        @minimize="handleMinimize"
        @maximize="handleMaximize"
        @close="$emit('close')"
      />

      <div class="main-content">
        <ChatHistory ref="chatHistory" @common-question-added="handleCommonQuestionAdded" />
        <ChatMainArea ref="chatMainArea" @refresh-history="handleRefreshHistory"/>
        <AiToolbox />
      </div>
    </div>
  </div>
</template>

<script>

import ChatHeader from './components/ChatHeader.vue'
import ChatHistory from './components/ChatHistory.vue'
import ChatMainArea from './components/ChatMainArea.vue'
import AiToolbox from './components/AiToolbox.vue'

export default {
  name: 'ChatLayout',
  components: {
    ChatHeader,
    ChatHistory,
    ChatMainArea,
    AiToolbox
  },
  props: {
    fileChatContext: {
      type: Object,
      default: null
    },
    showHeader: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      isInitialized: false
    }
  },

  async created() {
    // 完全重置所有状态，避免文件对话和直接打开的混乱
    this.$store.dispatch('chat-v3/reset')

    console.log('this.fileChatContext', this.fileChatContext)

    if (this.fileChatContext) {
      // 文件聊天模式
      try {
        // 直接使用 commit 进行文件聊天初始化
        const knowledge = {
          id: this.fileChatContext.knowledgeName,
          label: this.fileChatContext.knowledgeName
        }
        this.$store.commit('chat-v3/SET_CURRENT_KNOWLEDGE', knowledge)
        this.$store.commit('chat-v3/SET_ACTIVE_FILES', this.fileChatContext.files || [])
        this.isInitialized = true
      } catch (error) {
        console.error('文件聊天初始化失败:', error)
        this.$message.error('文件聊天初始化失败，请重试')
      }
    } else {
      // 正常聊天模式
      this.isInitialized = true
    }
  },
  methods: {
    handleMinimize() {
      if (this.$parent && this.$parent.handleMinimize) {
        this.$parent.handleMinimize()
      } else {
        this.$emit('minimize')
      }
    },

    handleMaximize() {
      if (this.$parent && this.$parent.handleMaximize) {
        this.$parent.handleMaximize()
      } else {
        this.$emit('maximize')
      }
    },

    handleCommonQuestionAdded() {
      // 通知 ChatMainArea 刷新常用问题
      if (this.$refs.chatMainArea) {
        this.$refs.chatMainArea.handleUpdateHelpList();
      }
    },

    // 处理历史列表刷新事件
    handleRefreshHistory() {
      // 刷新左侧历史列表
      if (this.$refs.chatHistory) {
        this.$refs.chatHistory.fetchChatHistory();
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.chat-v3-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f7f8fb;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  position: relative;
}

.global-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 9999;

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #409eff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  .loading-text {
    margin-top: 16px;
    color: #666;
    font-size: 14px;
  }
}


.main-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.main-content {
  display: flex;
  flex: 1;
  overflow: hidden;
  min-height: 0;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
  }
}
</style>
