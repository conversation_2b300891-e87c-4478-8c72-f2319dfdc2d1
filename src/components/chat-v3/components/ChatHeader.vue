<template>
  <header class="header" :class="{ 'minimized': isMinimized }" @dblclick="handleDoubleClick">
    <div class="logo">
      <div class="logo-icon">
        <img src="../icons/logo.svg" alt="Logo" width="22" height="16" />
      </div>
      <span class="logo-text" :title="isMinimized ? '拖拽移动窗口，双击还原' : title">{{ title }}</span>
      <!-- 最小化状态下的拖拽指示器 -->
      <div v-if="isMinimized" class="drag-indicator" title="可拖拽移动">
        ⋮⋮
      </div>
    </div>
    <div class="header-actions">
      <button
        class="action-btn"
        @click="$emit('minimize')"
        :title="isMinimized ? '还原' : '最小化'"
        :disabled="isFullscreen"
      >
        <img src="../icons/minimize.svg" alt="Minimize" width="12" height="2" />
      </button>
      <button
        class="action-btn"
        @click="$emit('maximize')"
        :title="isFullscreen ? '还原' : '全屏'"
        :disabled="isMinimized"
      >
        <img
          :src="isFullscreen ? require('../icons/restore.svg') : require('../icons/maximize.svg')"
          :alt="isFullscreen ? 'Restore' : 'Maximize'"
          width="14"
          height="14"
        />
      </button>
      <button class="action-btn" @click="$emit('close')">
        <img src="../icons/close.svg" alt="Close" width="10" height="12" />
      </button>
    </div>
  </header>
</template>

<script>
export default {
  name: 'ChatHeader',
  props: {
    title: {
      type: String,
      default: '知识库·AI助手'
    },
    isFullscreen: {
      type: Boolean,
      default: false
    },
    isMinimized: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    handleDoubleClick() {
      if (this.isFullscreen || this.isMinimized) {
        this.$emit(this.isFullscreen ? 'maximize' : 'minimize')
      } else {
        this.$emit('maximize')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.header {
  display: flex;
  align-items: center;
  padding: 0 20px;
  height: 60px;
  background-color: #ffffff;
  border-bottom: 1px solid #e8e8e8;
  flex-shrink: 0;
  user-select: none;
  cursor: default;
}

.logo {
  display: flex;
  align-items: center;
  position: relative;
}

.logo-icon {
  width: 32px;
  height: 32px;
  background: linear-gradient(to bottom right, #26a4fe, #9038f7, #ff68cf);
  border-radius: 50%;
  margin-right: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-icon svg {
  width: 22px;
  height: 16px;
}

.logo-text {
  font-weight: bold;
  font-size: 16px;
}

.header-actions {
  margin-left: auto;
  display: flex;
  gap: 10px;
}

.action-btn {
  background: none;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  cursor: pointer;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn:hover {
  background-color: #f5f5f5;
}

.action-btn:disabled {
  opacity: 0.4;
  cursor: not-allowed;
}

.drag-indicator {
  margin-right: 8px;
  color: #999;
  font-size: 16px;
  cursor: move;
  user-select: none;
  font-weight: bold;
}
.header.minimized {
  padding: 0 10px;
  border-bottom: none;

  .logo {
    flex: 1;
    min-width: 0;
  }

  .logo-icon {
    width: 24px;
    height: 24px;
    margin-right: 8px;
    flex-shrink: 0;
  }

  .logo-text {
    font-size: 14px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
    min-width: 0;
  }

  .header-actions {
    gap: 5px;
    flex-shrink: 0;

    .action-btn {
      padding: 4px;
      width: 24px;
      height: 24px;

      img {
        width: 12px;
        height: 12px;
      }
    }
  }
}
</style>
