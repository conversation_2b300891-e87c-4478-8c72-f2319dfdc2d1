<template>
  <div class="container">
    <z-dialog
      :title="chatDialog.titleChat"
      :show.sync="chatDialog.openChat"
      ref="zdialog"
      v-if="chatDialog.openChat"
      @closeOnClickModal="closeOnClickModal"
      @handleMinimize="handleMinimize"
      :isFooter="false"
    >
      <div slot="body" class="content">
        <!-- 知识库选择 -->
        <KnowledgeSelect
          @getknowledgeValue="getknowledgeValue"
          v-show="
            !this.$store.state.common.files.length &&
            !this.$store.state.common.isEnterpriseKnowledge
          "
          ref = "KnowledgeSelect"
        />
        <div
          class="contentMain"
          :style="{
            height: !(
              !this.$store.state.common.files.length &&
              !this.$store.state.common.isEnterpriseKnowledge
            )
              ? 'calc(100vh - 120px)'
              : 'calc(100vh - 190px)',
          }"
        >
          <!-- 左侧对话列表 历史记录  功能 -->
          <LeftOptionList
            ref="LeftOptionList"
            @getUseFaq="getUseFaq"
            @getOptions="getOptions"
            @getHistory="getHistory"
            @updataTopK="updataTopK"
          />
          <!-- 对话框主体 -->
          <ChatMain
            ref="ChatMain"
            @updataAskList="updataAskList"
            @handleMinimize="
              () => {
                this.$refs.zdialog.minimize();
              }
            "
          />
		  <RightOptionList></RightOptionList>
        </div>
      </div>
      <div slot="footer">对话框footer</div>
    </z-dialog>
  </div>
</template>

<script>
import ZDialog from "@/components/ZDialog";
import ChatMain from "./components/ChatMain.vue";
import LeftOptionList from "./components/LeftOptionList.vue";
import RightOptionList from "./components/RightOptionList.vue";
import KnowledgeSelect from "./components/KnowledgeSelect.vue";
import { getChatSetting } from "./components/commonReq.js";

export default {
  name: "",
  components: { ZDialog, LeftOptionList, ChatMain, KnowledgeSelect,RightOptionList },
  props: {
    oldChat:{
      type:String
    }
  },
  data() {
    return {
      // 聊天对话框配置
      chatDialog: {
        titleChat: "知识库AI助手",
        openChat: false,
        isShrink: true,
        chatParams: {
          knowledge_base_name: "",
          knowledgeList: [],
        },
      },
      flag: "",
      knowledge_name: "",
      setKnowledge: "",
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    open() {
      this.chatDialog.openChat = true;
      // 等待弹窗完全打开后设置为全屏
      this.$nextTick(() => {
        if (this.$refs.zdialog) {
          // this.$refs.zdialog.setFullscreen();
        }
      });
    },
    //关闭最小化弹框
    closeMinimize() {
      this.$refs.zdialog.closeDialog();
      setTimeout(() => {
        this.chatDialog.openChat = true;
        // 等待弹窗完全打开后设置为全屏
        this.$nextTick(() => {
          if (this.$refs.zdialog) {
            this.$refs.zdialog.setFullscreen();
          }
        });
      }, 200);
    },
    handleMinimize() {
      this.$emit("handleMinimize");
      // if (this.$store.state.common.isEnterpriseKnowledge) {
      //         this.$refs.KnowledgeSelect&&this.$refs.KnowledgeSelect.getList();
      // }
      // console.log(this.$store.state.common.isEnterpriseKnowledge);
      // 确保对话框状态正确关闭
      this.chatDialog.openChat = false;

      if (this.$store.state.common.isEnterpriseKnowledge) {
        this.$refs.KnowledgeSelect && this.$refs.KnowledgeSelect.getList();
      }

    },
    closeOnClickModal() {
      this.chatDialog.openChat = false;
      this.$emit("closeOnClickModal");
      this.$store.commit("common/isEnterpriseKnowledge", false);
    },
    // 获取选中知识库
    async getknowledgeValue(knowledge_name) {
      if (this.$store.state.common.files.length>0&&this.oldChat) {
               knowledge_name = this.oldChat
      }

      // 获取聊天初始化配置
      const { code, data } = await getChatSetting(knowledge_name);
      if (code === 200) {
        this.knowledge_name = knowledge_name;
        this.setKnowledge = data;
        this.$refs.LeftOptionList.initData({ ...data[0], knowledge_name });
        this.$refs.ChatMain.initData({ ...data[0], knowledge_name });
      }
    },
    // 常见问题回调
    getUseFaq(val) {
      this.$refs.ChatMain.askQuestion(val, "0");
    },
    // 功能中心回调
    getOptions(val) {
      this.$refs.ChatMain.askQuestion(val, "1");
    },
    // 对话列表回调
    getHistory(val) {
      this.$refs.ChatMain.askQuestion(val, "2");
    },
    // 更新左侧对话列表
    updataAskList() {
      this.$refs.LeftOptionList.getList();

    },
    updataTopK(val) {
      const { top_k } = val;
      let params = {
        ...this.setKnowledge[0],
        top_k,
        knowledge_name: this.knowledge_name,
      };
      this.$refs.LeftOptionList.initData(params);
      this.$refs.ChatMain.initData(params);
    },
  },
};
</script>

<style scoped lang="scss">
.content {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  .contentMain {
    // height: calc(100vh - 190px);
    width: 100%;
    display: flex;
  }
}

// 覆盖 z-dialog 的 header 样式，使其看起来像 chat-v3 的 ChatHeader
:deep(.z-dialog) {
  .dialog-header {
    display: flex;
    align-items: center;
    padding: 0 20px;
    height: 60px;
    background-color: #ffffff;
    border-bottom: 1px solid #e8e8e8;
    flex-shrink: 0;
    user-select: none;
    cursor: default;

    .dialog-title {
      display: flex;
      align-items: center;

      // 添加 logo 图标
      &::before {
        content: '';
        width: 32px;
        height: 32px;
        background: linear-gradient(to bottom right, #26a4fe, #9038f7, #ff68cf);
        border-radius: 50%;
        margin-right: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      font-weight: bold;
      font-size: 16px;
      color: #313233;
    }

    .dialog-controls {
      margin-left: auto;
      display: flex;
      gap: 10px;

      .control-btn {
        background: none;
        border: 1px solid #e8e8e8;
        border-radius: 4px;
        cursor: pointer;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
          background-color: #f5f5f5;
        }
      }
    }
  }
}
</style>
